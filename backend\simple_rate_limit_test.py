#!/usr/bin/env python3
"""
Simple Rate Limit Test Script

This script hits a specific endpoint 100 times to test the rate limiting system.
It will show you exactly when rate limiting kicks in and how it behaves.
"""

import requests
import time
import urllib3
from datetime import datetime

# Disable SSL warnings for localhost testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_endpoint_rate_limit():
    """Test rate limiting by hitting an endpoint 100 times."""
    
    # Configuration
    base_url = "https://localhost:5000"
    endpoint = "/api/auth/csrf-token"  # This endpoint has 60/minute rate limit
    full_url = f"{base_url}{endpoint}"
    
    print("Simple Rate Limit Test")
    print("=" * 50)
    print(f"Target URL: {full_url}")
    print(f"Expected rate limit: 60/minute")
    print(f"Test: Making 100 requests as fast as possible")
    print("=" * 50)
    
    # Track results
    results = []
    start_time = time.time()
    
    for i in range(1, 101):
        request_start = time.time()
        
        try:
            response = requests.get(
                full_url,
                verify=False,  # Skip SSL verification for localhost
                timeout=10
            )
            
            request_end = time.time()
            request_duration = request_end - request_start
            
            result = {
                'request_num': i,
                'status_code': response.status_code,
                'duration': request_duration,
                'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3],
                'success': response.status_code == 200,
                'rate_limited': response.status_code == 429,
                'retry_after': response.headers.get('Retry-After'),
                'error': None
            }
            
            # Print real-time results
            if response.status_code == 200:
                print(f"Request {i:3d}: ✅ SUCCESS (200) - {request_duration:.3f}s")
            elif response.status_code == 429:
                retry_after = response.headers.get('Retry-After', 'N/A')
                print(f"Request {i:3d}: 🚫 RATE LIMITED (429) - Retry-After: {retry_after}s")
            else:
                print(f"Request {i:3d}: ❌ ERROR ({response.status_code}) - {request_duration:.3f}s")
                
        except Exception as e:
            result = {
                'request_num': i,
                'status_code': None,
                'duration': None,
                'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3],
                'success': False,
                'rate_limited': False,
                'retry_after': None,
                'error': str(e)
            }
            print(f"Request {i:3d}: 💥 EXCEPTION - {str(e)}")
        
        results.append(result)
        
        # Small delay to avoid overwhelming the system
        time.sleep(0.01)  # 10ms delay
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # Analyze results
    print("\n" + "=" * 50)
    print("ANALYSIS")
    print("=" * 50)
    
    successful = [r for r in results if r['success']]
    rate_limited = [r for r in results if r['rate_limited']]
    errors = [r for r in results if r['error'] is not None]
    
    print(f"Total requests: {len(results)}")
    print(f"Total duration: {total_duration:.2f} seconds")
    print(f"Average rate: {len(results)/total_duration:.1f} requests/second")
    print()
    print(f"✅ Successful (200): {len(successful)}")
    print(f"🚫 Rate limited (429): {len(rate_limited)}")
    print(f"💥 Errors: {len(errors)}")
    
    if rate_limited:
        first_rate_limited = min(rate_limited, key=lambda x: x['request_num'])
        print(f"\nFirst rate limit hit at request #{first_rate_limited['request_num']}")
        if first_rate_limited['retry_after']:
            print(f"Retry-After header: {first_rate_limited['retry_after']} seconds")
    
    # Show when rate limiting started
    if len(successful) > 0 and len(rate_limited) > 0:
        print(f"\nRate limiting kicked in after {len(successful)} successful requests")
        print("This suggests the rate limiter is working correctly!")
    elif len(successful) == len(results):
        print("\n⚠️  No rate limiting occurred - this might indicate:")
        print("   - Rate limiting is disabled")
        print("   - The rate limit is higher than 100 requests")
        print("   - The server is not running")
    elif len(rate_limited) == len(results):
        print("\n⚠️  All requests were rate limited - this might indicate:")
        print("   - Previous test runs used up the rate limit")
        print("   - Rate limit is very low")
    
    # Show timing pattern
    if len(results) >= 10:
        print(f"\nTiming pattern (first 10 requests):")
        for i in range(10):
            r = results[i]
            status = "✅" if r['success'] else "🚫" if r['rate_limited'] else "❌"
            print(f"  {r['timestamp']} - Request {r['request_num']:2d}: {status} ({r['status_code']})")
    
    return results

if __name__ == "__main__":
    print("Starting rate limit test...")
    print("Make sure your Flask server is running on https://localhost:5000")
    print()
    
    try:
        results = test_endpoint_rate_limit()
        print(f"\nTest completed! Check the results above.")
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\n\nTest failed with error: {e}")
        print("Make sure your Flask server is running and accessible.")
