{"name": "@fullcalendar/list", "version": "6.1.17", "title": "FullCalendar List View Plugin", "description": "Display events on a calendar view that looks like a bulleted list", "keywords": ["calendar", "event", "full-sized", "fullcalendar", "list-view"], "homepage": "https://fullcalendar.io/docs/list-view", "peerDependencies": {"@fullcalendar/core": "~6.1.17"}, "type": "module", "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar.git", "directory": "packages/list"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "copyright": "2024 <PERSON>", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "unpkg": "./index.global.min.js", "jsdelivr": "./index.global.min.js", "exports": {"./package.json": "./package.json", "./index.cjs": "./index.cjs", "./index.js": "./index.js", ".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}, "./internal.cjs": "./internal.cjs", "./internal.js": "./internal.js", "./internal": {"types": "./internal.d.ts", "require": "./internal.cjs", "import": "./internal.js"}}, "sideEffects": false}