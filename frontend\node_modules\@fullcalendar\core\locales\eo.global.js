/*!
FullCalendar Core v6.1.17
Docs & License: https://fullcalendar.io
(c) 2024 <PERSON>
*/
(function (index_js) {
    'use strict';

    var locale = {
        code: 'eo',
        week: {
            dow: 1,
            doy: 4, // The week that contains Jan 4th is the first week of the year.
        },
        buttonText: {
            prev: 'Antaŭa',
            next: 'Sekva',
            today: 'Hodia<PERSON>',
            year: 'J<PERSON>',
            month: 'Monato',
            week: 'Se<PERSON>jn<PERSON>',
            day: 'Tag<PERSON>',
            list: 'Tagordo',
        },
        weekText: 'Sm',
        allDayText: 'Tuta tago',
        moreLinkText: 'pli',
        noEventsText: 'Neniuj eventoj por montri',
    };

    index_js.globalLocales.push(locale);

})(FullCalendar);
